'use client';

import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';

import { api } from '@/trpc/react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: string;
  isEmailVerified: boolean;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean
  ) => Promise<{ success: boolean; error?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
  acceptedTerms: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // tRPC mutations
  const loginMutation = api.auth.login.useMutation();
  const registerMutation = api.auth.register.useMutation();
  const logoutMutation = api.auth.logout.useMutation();
  const refreshMutation = api.auth.refreshToken.useMutation();

  // tRPC queries
  const { data: currentUser, refetch: refetchUser } = api.auth.me.useQuery(undefined, {
    enabled: false, // We'll manually trigger this
    retry: false,
  });

  const isAuthenticated = !!user;

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Try to refresh token first
      const refreshResult = await refreshMutation.mutateAsync();

      if (refreshResult.success && refreshResult.user) {
        setUser(refreshResult.user);
      } else {
        // If refresh fails, try to get current user
        const userResult = await refetchUser();
        if (userResult.data) {
          setUser(userResult.data);
        }
      }
    } catch (error) {
      // If both fail, user is not authenticated
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      setIsLoading(true);

      const result = await loginMutation.mutateAsync({
        email,
        password,
        rememberMe,
      });

      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        return { success: false, error: 'Login failed' };
      }
    } catch (error: any) {
      const errorMessage = error?.message || 'Login failed';
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      setIsLoading(true);

      const result = await registerMutation.mutateAsync(userData);

      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        return { success: false, error: 'Registration failed' };
      }
    } catch (error: any) {
      const errorMessage = error?.message || 'Registration failed';
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await logoutMutation.mutateAsync();
      setUser(null);
    } catch (error) {
      // Even if logout fails on server, clear local state
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAuth = async () => {
    try {
      const result = await refreshMutation.mutateAsync();

      if (result.success && result.user) {
        setUser(result.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for checking if user has specific role
export function useRole(requiredRole: string | string[]) {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return false;
  }

  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return roles.includes(user.role);
}

// Hook for checking if user is admin
export function useIsAdmin() {
  return useRole('admin');
}

// Hook for checking if user is recruiter or higher
export function useIsRecruiter() {
  return useRole(['admin', 'recruiter', 'hr']);
}

// Hook for checking authentication status
export function useAuthStatus() {
  const { isAuthenticated, isLoading, user } = useAuth();

  return {
    isAuthenticated,
    isLoading,
    user,
    isGuest: !isAuthenticated && !isLoading,
  };
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requiredRole?: string | string[];
    redirectTo?: string;
    fallback?: React.ComponentType;
  } = {}
) {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, isLoading, user } = useAuth();
    const { requiredRole, fallback: Fallback } = options;

    if (isLoading) {
      return <div>Loading...</div>;
    }

    if (!isAuthenticated) {
      if (Fallback) {
        return <Fallback />;
      }
      return <div>Please log in to access this page.</div>;
    }

    if (requiredRole && user) {
      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      if (!roles.includes(user.role)) {
        if (Fallback) {
          return <Fallback />;
        }
        return <div>You do not have permission to access this page.</div>;
      }
    }

    return <Component {...props} />;
  };
}

export default AuthContext;
