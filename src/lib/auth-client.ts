'use client';

/**
 * Client-side authentication utilities
 */

// Check if user is authenticated (client-side)
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false;

  // Check if access token exists in cookies
  return document.cookie.includes('accessToken=');
}

// Get user info from cookies (client-side)
export function getUserFromCookies(): { id: string; email: string; role: string } | null {
  if (typeof window === 'undefined') return null;

  try {
    // This is a simplified version - in a real app, you might store user info differently
    // or decode it from the JWT token (though storing sensitive data in localStorage is not recommended)
    const userInfo = localStorage.getItem('userInfo');
    return userInfo ? JSON.parse(userInfo) : null;
  } catch {
    return null;
  }
}

// Store user info (client-side)
export function storeUserInfo(user: { id: string; email: string; role: string }): void {
  if (typeof window === 'undefined') return;

  localStorage.setItem('userInfo', JSON.stringify(user));
}

// Clear user info (client-side)
export function clearUserInfo(): void {
  if (typeof window === 'undefined') return;

  localStorage.removeItem('userInfo');
}

// Check if user has specific role (client-side)
export function hasRole(requiredRole: string | string[]): boolean {
  const user = getUserFromCookies();
  if (!user) return false;

  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return roles.includes(user.role);
}

// Check if user is admin (client-side)
export function isAdmin(): boolean {
  return hasRole('admin');
}

// Check if user is recruiter or higher (client-side)
export function isRecruiter(): boolean {
  return hasRole(['admin', 'recruiter', 'hr']);
}

// Logout function (client-side)
export async function logout(): Promise<void> {
  try {
    // Call logout API
    await fetch('/api/trpc/auth.logout', {
      method: 'POST',
      credentials: 'include',
    });
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // Clear local storage
    clearUserInfo();

    // Redirect to login page
    window.location.href = '/login';
  }
}

// Refresh token function (client-side)
export async function refreshToken(): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      credentials: 'include',
    });

    if (response.ok) {
      const data = await response.json();
      if (data.user) {
        storeUserInfo({
          id: data.user.id,
          email: data.user.email,
          role: data.user.role,
        });
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Token refresh error:', error);
    return false;
  }
}

// Auto-refresh token before expiration
export function setupTokenRefresh(): void {
  if (typeof window === 'undefined') return;

  // Refresh token every 10 minutes (access token expires in 15 minutes)
  const refreshInterval = 10 * 60 * 1000; // 10 minutes

  const intervalId = setInterval(async () => {
    if (isAuthenticated()) {
      const success = await refreshToken();
      if (!success) {
        // If refresh fails, clear interval and logout
        clearInterval(intervalId);
        await logout();
      }
    } else {
      // If not authenticated, clear interval
      clearInterval(intervalId);
    }
  }, refreshInterval);

  // Store interval ID to clear it later if needed
  (window as any).__authRefreshInterval = intervalId;
}

// Clear token refresh interval
export function clearTokenRefresh(): void {
  if (typeof window === 'undefined') return;

  const intervalId = (window as any).__authRefreshInterval;
  if (intervalId) {
    clearInterval(intervalId);
    delete (window as any).__authRefreshInterval;
  }
}

// Handle authentication errors
export function handleAuthError(error: any): void {
  if (error?.status === 401 || error?.code === 'UNAUTHORIZED') {
    // Token expired or invalid, try to refresh
    refreshToken().then((success) => {
      if (!success) {
        // Refresh failed, logout user
        logout();
      }
    });
  }
}

// Get CSRF token from meta tag (if using CSRF protection)
export function getCSRFToken(): string | null {
  if (typeof window === 'undefined') return null;

  const metaTag = document.querySelector('meta[name="csrf-token"]');
  return metaTag ? metaTag.getAttribute('content') : null;
}

// Add auth headers to fetch requests
export function getAuthHeaders(): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Add CSRF token if available
  const csrfToken = getCSRFToken();
  if (csrfToken) {
    headers['X-CSRF-Token'] = csrfToken;
  }

  return headers;
}

// Authenticated fetch wrapper
export async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const authHeaders = getAuthHeaders();

  const response = await fetch(url, {
    ...options,
    credentials: 'include', // Include cookies
    headers: {
      ...authHeaders,
      ...options.headers,
    },
  });

  // Handle auth errors
  if (response.status === 401) {
    handleAuthError({ status: 401 });
  }

  return response;
}

// Check if current page requires authentication
export function requiresAuth(pathname: string): boolean {
  const protectedRoutes = [
    '/dashboard',
    '/profile',
    '/organizations',
    '/jobs/create',
    '/jobs/manage',
    '/applications',
    '/admin',
    '/recruiter',
  ];

  return protectedRoutes.some((route) => pathname.startsWith(route));
}

// Check if current page requires specific role
export function requiresRole(pathname: string): string[] | null {
  if (pathname.startsWith('/admin')) {
    return ['admin'];
  }

  if (
    pathname.startsWith('/jobs/create') ||
    pathname.startsWith('/jobs/manage') ||
    pathname.startsWith('/recruiter')
  ) {
    return ['admin', 'recruiter', 'hr'];
  }

  return null;
}

// Redirect to login if not authenticated
export function redirectToLogin(currentPath?: string): void {
  if (typeof window === 'undefined') return;

  const loginUrl = new URL('/login', window.location.origin);
  if (currentPath) {
    loginUrl.searchParams.set('redirect', currentPath);
  }

  window.location.href = loginUrl.toString();
}

// Redirect to unauthorized page
export function redirectToUnauthorized(): void {
  if (typeof window === 'undefined') return;

  window.location.href = '/unauthorized';
}
