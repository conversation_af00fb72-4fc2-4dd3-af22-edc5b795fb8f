import { NextRequest, NextResponse } from 'next/server';
import { dbConnect } from '@/db/connection';
import { User } from '@/db/models';
import { env } from '@/env';
import {
  parseOAuthState,
  exchangeOAuthCode,
  fetchGoogleUserProfile,
  normalizeGoogleUser,
  handleOAuthError,
} from '@/lib/oauth';
import jwt from 'jsonwebtoken';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign(
    { userId, email, role, type: 'access' },
    JWT_ACCESS_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRES_IN }
  );
}

function generateRefreshToken(userId: string) {
  return jwt.sign(
    { userId, type: 'refresh' },
    JWT_REFRESH_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRES_IN }
  );
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    
    // Handle OAuth errors
    if (error) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('error', `oauth_error_${error}`);
      return NextResponse.redirect(loginUrl);
    }
    
    // Validate required parameters
    if (!code || !state) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('error', 'oauth_invalid_params');
      return NextResponse.redirect(loginUrl);
    }
    
    // Parse and validate state
    const oauthState = parseOAuthState(state);
    if (!oauthState || oauthState.provider !== 'google') {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('error', 'oauth_invalid_state');
      return NextResponse.redirect(loginUrl);
    }
    
    // Exchange code for tokens
    const tokens = await exchangeOAuthCode('google', code);
    
    // Fetch user profile
    const googleProfile = await fetchGoogleUserProfile(tokens.accessToken);
    const normalizedUser = normalizeGoogleUser(googleProfile);
    
    // Find or create user
    let user = await User.findOne({
      $or: [
        { email: normalizedUser.email },
        { 'oauthProviders.google.id': normalizedUser.providerId },
      ],
      isDeleted: { $ne: true },
    });
    
    if (user) {
      // Update existing user with OAuth info
      if (!user.oauthProviders) {
        user.oauthProviders = {};
      }
      
      user.oauthProviders.google = {
        id: normalizedUser.providerId,
        email: normalizedUser.email,
        connectedAt: new Date(),
      };
      
      // Update user info if not set
      if (!user.avatar && normalizedUser.avatar) {
        user.avatar = normalizedUser.avatar;
      }
      
      if (!user.isEmailVerified && normalizedUser.isEmailVerified) {
        user.isEmailVerified = true;
        user.emailVerifiedAt = new Date();
      }
      
      user.lastLoginAt = new Date();
      await user.save();
    } else {
      // Create new user
      const userData = {
        email: normalizedUser.email,
        firstName: normalizedUser.firstName,
        lastName: normalizedUser.lastName,
        avatar: normalizedUser.avatar,
        isEmailVerified: normalizedUser.isEmailVerified,
        emailVerifiedAt: normalizedUser.isEmailVerified ? new Date() : undefined,
        role: 'candidate' as const,
        status: 'active' as const,
        oauthProviders: {
          google: {
            id: normalizedUser.providerId,
            email: normalizedUser.email,
            connectedAt: new Date(),
          },
        },
        lastLoginAt: new Date(),
      };
      
      user = await User.createUser(userData);
    }
    
    // Generate JWT tokens
    const accessToken = generateAccessToken(user.userId, user.email, user.role);
    const refreshToken = generateRefreshToken(user.userId);
    
    // Determine redirect URL
    const redirectTo = oauthState.redirectTo || '/dashboard';
    const redirectUrl = new URL(redirectTo, request.url);
    
    // Create response with redirect
    const response = NextResponse.redirect(redirectUrl);
    
    // Set authentication cookies
    response.cookies.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
    response.cookies.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
    
    return response;
  } catch (error) {
    console.error('Google OAuth callback error:', error);
    
    // Redirect to login with error
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('error', 'oauth_callback_failed');
    
    return NextResponse.redirect(loginUrl);
  }
}
