import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { dbConnect } from '@/db/connection';
import { User } from '@/db/models';
import { env } from '@/env';
import {
  validateOTP,
  OTP_ERRORS,
  OTPError,
} from '@/lib/otp';
import { sendWelcomeEmail } from '@/lib/email';
import jwt from 'jsonwebtoken';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign(
    { userId, email, role, type: 'access' },
    JWT_ACCESS_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRES_IN }
  );
}

function generateRefreshToken(userId: string) {
  return jwt.sign(
    { userId, type: 'refresh' },
    JWT_REFRESH_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRES_IN }
  );
}

const VerifyOTPSchema = z.object({
  email: z.string().email('Invalid email address'),
  code: z.string().length(6, 'OTP must be 6 digits'),
  purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
});

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { email, code, purpose } = VerifyOTPSchema.parse(body);
    
    // Validate OTP
    const validation = validateOTP({
      email,
      code,
      purpose,
      deleteAfterVerification: true,
    });
    
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: 'Invalid OTP',
          message: validation.error || 'The OTP code is invalid or expired',
          attemptsRemaining: validation.attemptsRemaining,
        },
        { status: 400 }
      );
    }
    
    // Handle different purposes
    switch (purpose) {
      case 'login': {
        // Find user
        const user = await User.findOne({
          email: email.toLowerCase(),
          isDeleted: { $ne: true },
        });
        
        if (!user) {
          return NextResponse.json(
            {
              error: 'User not found',
              message: 'No account found with this email address',
            },
            { status: 404 }
          );
        }
        
        if (user.status !== 'active') {
          return NextResponse.json(
            {
              error: 'Account inactive',
              message: 'Your account is not active. Please contact support.',
            },
            { status: 403 }
          );
        }
        
        // Update last login
        user.lastLoginAt = new Date();
        await user.save();
        
        // Generate JWT tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);
        
        // Create response
        const response = NextResponse.json({
          success: true,
          message: 'Login successful',
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
        });
        
        // Set authentication cookies
        response.cookies.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
        response.cookies.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
        
        return response;
      }
      
      case 'verification': {
        // Find user
        const user = await User.findOne({
          email: email.toLowerCase(),
          isDeleted: { $ne: true },
        });
        
        if (!user) {
          return NextResponse.json(
            {
              error: 'User not found',
              message: 'No account found with this email address',
            },
            { status: 404 }
          );
        }
        
        // Verify email
        if (!user.isEmailVerified) {
          user.isEmailVerified = true;
          user.emailVerifiedAt = new Date();
          await user.save();
          
          // Send welcome email for new users
          if (user.createdAt && Date.now() - user.createdAt.getTime() < 24 * 60 * 60 * 1000) {
            await sendWelcomeEmail(user.email, `${user.firstName} ${user.lastName}`);
          }
        }
        
        return NextResponse.json({
          success: true,
          message: 'Email verified successfully',
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
        });
      }
      
      case 'password-reset': {
        // For password reset, return a temporary token that can be used to reset password
        const resetToken = jwt.sign(
          { email, purpose: 'password-reset', verified: true },
          JWT_ACCESS_SECRET,
          { expiresIn: '15m' }
        );
        
        return NextResponse.json({
          success: true,
          message: 'OTP verified. You can now reset your password.',
          resetToken,
        });
      }
      
      default: {
        return NextResponse.json(
          {
            error: 'Invalid purpose',
            message: 'Invalid OTP purpose',
          },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error('OTP verification error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    if (error instanceof OTPError) {
      return NextResponse.json(
        {
          error: error.code,
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}
