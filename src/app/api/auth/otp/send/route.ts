import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { dbConnect } from '@/db/connection';
import { User } from '@/db/models';
import {
  generateAndStoreOTP,
  checkOTPRateLimit,
  OTP_ERRORS,
  OTPError,
} from '@/lib/otp';
import { sendOTPEmail } from '@/lib/email';

const SendOTPSchema = z.object({
  email: z.string().email('Invalid email address'),
  purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
});

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { email, purpose } = SendOTPSchema.parse(body);
    
    // Check rate limiting
    const rateLimit = checkOTPRateLimit(email);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: 'Too many OTP requests. Please try again later.',
          resetTime: rateLimit.resetTime,
        },
        { status: 429 }
      );
    }
    
    // For login and password reset, check if user exists
    if (purpose === 'login' || purpose === 'password-reset') {
      const user = await User.findOne({
        email: email.toLowerCase(),
        isDeleted: { $ne: true },
      });
      
      if (!user) {
        // Don't reveal if user exists or not for security
        return NextResponse.json({
          success: true,
          message: 'If an account with this email exists, an OTP has been sent.',
        });
      }
      
      // Check if user account is active
      if (user.status !== 'active') {
        return NextResponse.json(
          {
            error: 'Account inactive',
            message: 'Your account is not active. Please contact support.',
          },
          { status: 403 }
        );
      }
    }
    
    // Generate and store OTP
    const { record, code } = await generateAndStoreOTP(email, purpose);
    
    // Send OTP email
    const emailSent = await sendOTPEmail(email, code, purpose);
    
    if (!emailSent) {
      throw new OTPError(
        OTP_ERRORS.EMAIL_SEND_FAILED,
        'Failed to send OTP email'
      );
    }
    
    // Log OTP generation (in production, save to database)
    console.log(`OTP generated for ${email} (${purpose}):`, {
      code: code.substring(0, 2) + '****',
      expiresAt: record.expiresAt,
    });
    
    return NextResponse.json({
      success: true,
      message: 'OTP sent successfully',
      expiresAt: record.expiresAt,
      remainingAttempts: rateLimit.remainingAttempts,
    });
  } catch (error) {
    console.error('OTP send error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    if (error instanceof OTPError) {
      return NextResponse.json(
        {
          error: error.code,
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}
