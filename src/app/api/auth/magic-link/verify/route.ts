import { NextRequest, NextResponse } from 'next/server';
import { dbConnect } from '@/db/connection';
import { User } from '@/db/models';
import { env } from '@/env';
import {
  validateMagicLinkToken,
  markTokenAsUsed,
  MAGIC_LINK_ERRORS,
  MagicLinkError,
} from '@/lib/magic-link';
import { sendWelcomeEmail } from '@/lib/email';
import jwt from 'jsonwebtoken';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign(
    { userId, email, role, type: 'access' },
    JWT_ACCESS_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRES_IN }
  );
}

function generateRefreshToken(userId: string) {
  return jwt.sign(
    { userId, type: 'refresh' },
    JWT_REFRESH_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRES_IN }
  );
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get('token');
    
    if (!token) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('error', 'missing_token');
      return NextResponse.redirect(loginUrl);
    }
    
    // Validate magic link token
    const validation = validateMagicLinkToken(token);
    
    if (!validation.valid || !validation.payload) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('error', 'invalid_magic_link');
      return NextResponse.redirect(loginUrl);
    }
    
    const { email, purpose, redirectTo } = validation.payload;
    
    // Mark token as used
    markTokenAsUsed(token);
    
    // Handle different purposes
    switch (purpose) {
      case 'login': {
        // Find user
        const user = await User.findOne({
          email: email.toLowerCase(),
          isDeleted: { $ne: true },
        });
        
        if (!user) {
          const loginUrl = new URL('/login', request.url);
          loginUrl.searchParams.set('error', 'user_not_found');
          return NextResponse.redirect(loginUrl);
        }
        
        if (user.status !== 'active') {
          const loginUrl = new URL('/login', request.url);
          loginUrl.searchParams.set('error', 'account_inactive');
          return NextResponse.redirect(loginUrl);
        }
        
        // Update last login
        user.lastLoginAt = new Date();
        await user.save();
        
        // Generate JWT tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);
        
        // Redirect to destination
        const redirectUrl = new URL(redirectTo || '/dashboard', request.url);
        const response = NextResponse.redirect(redirectUrl);
        
        // Set authentication cookies
        response.cookies.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
        response.cookies.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
        
        return response;
      }
      
      case 'verification': {
        // Find user
        const user = await User.findOne({
          email: email.toLowerCase(),
          isDeleted: { $ne: true },
        });
        
        if (!user) {
          const loginUrl = new URL('/login', request.url);
          loginUrl.searchParams.set('error', 'user_not_found');
          return NextResponse.redirect(loginUrl);
        }
        
        // Verify email
        if (!user.isEmailVerified) {
          user.isEmailVerified = true;
          user.emailVerifiedAt = new Date();
          await user.save();
          
          // Send welcome email for new users
          if (user.createdAt && Date.now() - user.createdAt.getTime() < 24 * 60 * 60 * 1000) {
            await sendWelcomeEmail(user.email, `${user.firstName} ${user.lastName}`);
          }
        }
        
        // Generate JWT tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);
        
        // Redirect to destination with success message
        const redirectUrl = new URL(redirectTo || '/dashboard', request.url);
        redirectUrl.searchParams.set('verified', 'true');
        const response = NextResponse.redirect(redirectUrl);
        
        // Set authentication cookies
        response.cookies.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
        response.cookies.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
        
        return response;
      }
      
      case 'password-reset': {
        // Redirect to password reset form
        const resetUrl = new URL('/reset-password', request.url);
        resetUrl.searchParams.set('token', token);
        resetUrl.searchParams.set('email', email);
        
        return NextResponse.redirect(resetUrl);
      }
      
      default: {
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('error', 'invalid_purpose');
        return NextResponse.redirect(loginUrl);
      }
    }
  } catch (error) {
    console.error('Magic link verification error:', error);
    
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('error', 'verification_failed');
    return NextResponse.redirect(loginUrl);
  }
}
