import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { dbConnect } from '@/db/connection';
import { User } from '@/db/models';
import {
  createSecureMagicLink,
  checkMagicLinkRateLimit,
  MAGIC_LINK_ERRORS,
  MagicLinkError,
} from '@/lib/magic-link';
import { sendMagicLinkEmail } from '@/lib/email';

const SendMagicLinkSchema = z.object({
  email: z.string().email('Invalid email address'),
  purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
  redirectTo: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { email, purpose, redirectTo } = SendMagicLinkSchema.parse(body);
    
    // Check rate limiting
    const rateLimit = checkMagicLinkRateLimit(email);
    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: 'Too many magic link requests. Please try again later.',
          resetTime: rateLimit.resetTime,
        },
        { status: 429 }
      );
    }
    
    // For login and password reset, check if user exists
    if (purpose === 'login' || purpose === 'password-reset') {
      const user = await User.findOne({
        email: email.toLowerCase(),
        isDeleted: { $ne: true },
      });
      
      if (!user) {
        // Don't reveal if user exists or not for security
        return NextResponse.json({
          success: true,
          message: 'If an account with this email exists, a magic link has been sent.',
        });
      }
      
      // Check if user account is active
      if (user.status !== 'active') {
        return NextResponse.json(
          {
            error: 'Account inactive',
            message: 'Your account is not active. Please contact support.',
          },
          { status: 403 }
        );
      }
    }
    
    // Generate magic link
    const { magicLink, token, expiresAt } = await createSecureMagicLink(
      email,
      purpose,
      redirectTo
    );
    
    // Send magic link email
    const emailSent = await sendMagicLinkEmail(email, magicLink);
    
    if (!emailSent) {
      throw new MagicLinkError(
        MAGIC_LINK_ERRORS.EMAIL_SEND_FAILED,
        'Failed to send magic link email'
      );
    }
    
    // Log magic link generation (in production, save to database)
    console.log(`Magic link generated for ${email} (${purpose}):`, {
      token: token.substring(0, 10) + '...',
      expiresAt,
    });
    
    return NextResponse.json({
      success: true,
      message: 'Magic link sent successfully',
      expiresAt,
      remainingAttempts: rateLimit.remainingAttempts,
    });
  } catch (error) {
    console.error('Magic link send error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    if (error instanceof MagicLinkError) {
      return NextResponse.json(
        {
          error: error.code,
          message: error.message,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}
