import jwt from 'jsonwebtoken';
import { NextRequest, NextResponse } from 'next/server';

import { User } from '@/db/models';
import dbConnect from '@/db/mongoose';
import { env } from '@/env';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign({ userId, email, role, type: 'access' }, JWT_ACCESS_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  });
}

function generateRefreshToken(userId: string) {
  return jwt.sign({ userId, type: 'refresh' }, JWT_REFRESH_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
  });
}

function verifyRefreshToken(token: string) {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET) as {
      userId: string;
      type: string;
    };
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Get refresh token from cookies
    const refreshToken = request.cookies.get('refreshToken')?.value;
    const redirectUrl = request.nextUrl.searchParams.get('redirect') || '/dashboard';

    if (!refreshToken) {
      // No refresh token, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', redirectUrl);
      return NextResponse.redirect(loginUrl);
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);
    if (!decoded || decoded.type !== 'refresh') {
      // Invalid refresh token, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', redirectUrl);
      const response = NextResponse.redirect(loginUrl);

      // Clear invalid cookies
      response.cookies.delete('accessToken');
      response.cookies.delete('refreshToken');

      return response;
    }

    // Get user from database
    const user = await User.findOne({
      userId: decoded.userId,
      isDeleted: { $ne: true },
    });

    if (!user) {
      // User not found, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', redirectUrl);
      const response = NextResponse.redirect(loginUrl);

      // Clear cookies
      response.cookies.delete('accessToken');
      response.cookies.delete('refreshToken');

      return response;
    }

    // Generate new tokens
    const newAccessToken = generateAccessToken(user.userId, user.email, user.role);
    const newRefreshToken = generateRefreshToken(user.userId);

    // Redirect to original destination
    const response = NextResponse.redirect(new URL(redirectUrl, request.url));

    // Set new cookies
    response.cookies.set('accessToken', newAccessToken, ACCESS_COOKIE_OPTIONS);
    response.cookies.set('refreshToken', newRefreshToken, REFRESH_COOKIE_OPTIONS);

    return response;
  } catch (error) {
    console.error('Refresh token error:', error);

    // On error, redirect to login
    const loginUrl = new URL('/login', request.url);
    const redirectUrl = request.nextUrl.searchParams.get('redirect') || '/dashboard';
    loginUrl.searchParams.set('redirect', redirectUrl);

    const response = NextResponse.redirect(loginUrl);

    // Clear cookies
    response.cookies.delete('accessToken');
    response.cookies.delete('refreshToken');

    return response;
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Get refresh token from cookies
    const refreshToken = request.cookies.get('refreshToken')?.value;

    if (!refreshToken) {
      return NextResponse.json({ error: 'No refresh token provided' }, { status: 401 });
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);
    if (!decoded || decoded.type !== 'refresh') {
      const response = NextResponse.json({ error: 'Invalid refresh token' }, { status: 401 });

      // Clear invalid cookies
      response.cookies.delete('accessToken');
      response.cookies.delete('refreshToken');

      return response;
    }

    // Get user from database
    const user = await User.findOne({
      userId: decoded.userId,
      isDeleted: { $ne: true },
    });

    if (!user) {
      const response = NextResponse.json({ error: 'User not found' }, { status: 401 });

      // Clear cookies
      response.cookies.delete('accessToken');
      response.cookies.delete('refreshToken');

      return response;
    }

    // Generate new tokens
    const newAccessToken = generateAccessToken(user.userId, user.email, user.role);
    const newRefreshToken = generateRefreshToken(user.userId);

    // Create response
    const response = NextResponse.json({
      user: {
        id: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
      },
      success: true,
    });

    // Set new cookies
    response.cookies.set('accessToken', newAccessToken, ACCESS_COOKIE_OPTIONS);
    response.cookies.set('refreshToken', newRefreshToken, REFRESH_COOKIE_OPTIONS);

    return response;
  } catch (error) {
    console.error('Refresh token error:', error);

    const response = NextResponse.json({ error: 'Internal server error' }, { status: 500 });

    // Clear cookies on error
    response.cookies.delete('accessToken');
    response.cookies.delete('refreshToken');

    return response;
  }
}
