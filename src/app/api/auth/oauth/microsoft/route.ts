import { NextRequest, NextResponse } from 'next/server';
import { generateOAuthState, getOAuthAuthorizationUrl } from '@/lib/oauth';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const redirectTo = searchParams.get('redirect') || '/dashboard';
    
    // Generate OAuth state
    const state = generateOAuthState('microsoft', redirectTo);
    
    // Get Microsoft authorization URL
    const authUrl = getOAuthAuthorizationUrl('microsoft', state);
    
    // Redirect to Microsoft OAuth
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Microsoft OAuth initiation error:', error);
    
    // Redirect to login with error
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('error', 'oauth_init_failed');
    
    return NextResponse.redirect(loginUrl);
  }
}
