'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';

interface OAuthButtonsProps {
  redirectTo?: string;
  disabled?: boolean;
  className?: string;
}

export function OAuthButtons({ redirectTo, disabled = false, className }: OAuthButtonsProps) {
  const handleOAuthLogin = (provider: 'google' | 'microsoft') => {
    const params = new URLSearchParams();
    if (redirectTo) {
      params.set('redirect', redirectTo);
    }
    
    const url = `/api/auth/oauth/${provider}?${params.toString()}`;
    window.location.href = url;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <Button
        type="button"
        variant="outline"
        className="w-full"
        disabled={disabled}
        onClick={() => handleOAuthLogin('google')}
      >
        <Icons.google className="mr-2 h-4 w-4" />
        Continue with Google
      </Button>
      
      <Button
        type="button"
        variant="outline"
        className="w-full"
        disabled={disabled}
        onClick={() => handleOAuthLogin('microsoft')}
      >
        <Icons.microsoft className="mr-2 h-4 w-4" />
        Continue with Microsoft
      </Button>
    </div>
  );
}

export default OAuthButtons;
