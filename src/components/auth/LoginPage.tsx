'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useState } from 'react';
// Traditional login form component
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Icons } from '@/components/ui/icons';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/hooks/useAuth';

import MagicLinkForm from './MagicLinkForm';
import OAuthButtons from './OAuthButtons';
import OTPForm from './OTPForm';

const LoginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof LoginSchema>;

function TraditionalLoginForm({ onSuccess }: { onSuccess?: () => void }) {
  const { login } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(LoginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      setError(null);
      const result = await login(data.email, data.password, data.rememberMe || false);

      if (result.success) {
        onSuccess?.();
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (error) {
      setError('An unexpected error occurred');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          {...register('email')}
          disabled={isSubmitting}
        />
        {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
      </div>

      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          placeholder="Enter your password"
          {...register('password')}
          disabled={isSubmitting}
        />
        {errors.password && <p className="text-sm text-destructive">{errors.password.message}</p>}
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox id="rememberMe" {...register('rememberMe')} disabled={isSubmitting} />
        <Label htmlFor="rememberMe" className="text-sm">
          Remember me
        </Label>
      </div>

      {error && (
        <Alert variant="destructive">
          <Icons.alertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        Sign in
      </Button>
    </form>
  );
}

export function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirect') || '/dashboard';
  const error = searchParams.get('error');
  const [activeTab, setActiveTab] = useState('oauth');

  const handleLoginSuccess = () => {
    router.push(redirectTo);
  };

  const handleOTPLoginSuccess = (_user: any) => {
    // User is already logged in via OTP verification
    router.push(redirectTo);
  };

  const handleMagicLinkSuccess = () => {
    // Magic link sent successfully
    console.log('Magic link sent successfully');
  };

  // Error messages mapping
  const getErrorMessage = (errorCode: string | null) => {
    if (!errorCode) return null;

    const errorMessages: Record<string, string> = {
      oauth_init_failed: 'Failed to initialize OAuth login. Please try again.',
      oauth_error_access_denied: 'OAuth access was denied. Please try again.',
      oauth_invalid_params: 'Invalid OAuth parameters. Please try again.',
      oauth_invalid_state: 'Invalid OAuth state. Please try again.',
      oauth_callback_failed: 'OAuth login failed. Please try again.',
      missing_token: 'Missing authentication token.',
      invalid_magic_link: 'Invalid or expired magic link. Please request a new one.',
      user_not_found: 'No account found with this email address.',
      account_inactive: 'Your account is not active. Please contact support.',
      verification_failed: 'Verification failed. Please try again.',
    };

    return errorMessages[errorCode] || 'An error occurred during authentication.';
  };

  const errorMessage = getErrorMessage(error);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">Welcome back</h1>
          <p className="text-sm text-muted-foreground">Choose your preferred way to sign in</p>
        </div>

        {errorMessage && (
          <Alert variant="destructive">
            <Icons.alertCircle className="h-4 w-4" />
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl">Sign in to your account</CardTitle>
            <CardDescription>Access your Hirelytics dashboard</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="oauth">Social</TabsTrigger>
                <TabsTrigger value="magic">Magic Link</TabsTrigger>
                <TabsTrigger value="otp">OTP</TabsTrigger>
                <TabsTrigger value="password">Password</TabsTrigger>
              </TabsList>

              <TabsContent value="oauth" className="space-y-4">
                <OAuthButtons redirectTo={redirectTo} className="mt-4" />
                <div className="text-center">
                  <p className="text-xs text-muted-foreground">
                    Sign in securely with your existing account
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="magic" className="space-y-4">
                <MagicLinkForm
                  purpose="login"
                  redirectTo={redirectTo}
                  onSuccess={handleMagicLinkSuccess}
                  className="mt-4"
                />
              </TabsContent>

              <TabsContent value="otp" className="space-y-4">
                <OTPForm purpose="login" onLoginSuccess={handleOTPLoginSuccess} className="mt-4" />
              </TabsContent>

              <TabsContent value="password" className="space-y-4">
                <TraditionalLoginForm onSuccess={handleLoginSuccess} />
                <div className="text-center">
                  <a
                    href="/forgot-password"
                    className="text-sm text-muted-foreground hover:text-primary underline"
                  >
                    Forgot your password?
                  </a>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <div className="text-center text-sm">
          <span className="text-muted-foreground">Don't have an account? </span>
          <a href="/register" className="font-medium text-primary hover:underline">
            Sign up
          </a>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
