'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/ui/icons';
import { api } from '@/trpc/react';

export function MagicLinkVerification() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  const verifyMagicLinkMutation = api.auth.verifyMagicLink.useMutation({
    onSuccess: (data) => {
      if (data.success) {
        // Redirect to the intended destination
        const redirectTo = data.redirectTo || '/dashboard';
        
        if (data.verified) {
          // Show success message for email verification
          router.push(`${redirectTo}?verified=true`);
        } else {
          router.push(redirectTo);
        }
      }
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  useEffect(() => {
    const token = searchParams.get('token');

    if (!token) {
      setError('Missing magic link token');
      return;
    }

    // Verify magic link
    verifyMagicLinkMutation.mutate({ token });
  }, [searchParams, verifyMagicLinkMutation]);

  if (error) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Magic Link Invalid
            </h1>
          </div>

          <Alert variant="destructive">
            <Icons.alertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              The magic link may have expired or already been used.
            </p>
            <button
              onClick={() => router.push('/login')}
              className="text-sm text-primary hover:underline"
            >
              Request a new magic link
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <Icons.spinner className="h-6 w-6 text-blue-600 animate-spin" />
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Verifying magic link...
          </h1>
          <p className="text-sm text-muted-foreground">
            Please wait while we verify your magic link.
          </p>
        </div>
      </div>
    </div>
  );
}

export default MagicLinkVerification;
