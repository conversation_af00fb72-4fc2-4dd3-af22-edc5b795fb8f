'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/ui/icons';
import { api } from '@/trpc/react';

interface OAuthCallbackProps {
  provider: 'google' | 'microsoft';
}

export function OAuthCallback({ provider }: OAuthCallbackProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  const handleOAuthCallbackMutation = api.auth.handleOAuthCallback.useMutation({
    onSuccess: (data) => {
      if (data.success && data.user) {
        // Redirect to the intended destination
        const redirectTo = data.redirectTo || '/dashboard';
        router.push(redirectTo);
      }
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    if (error) {
      setError(`OAuth error: ${error}`);
      return;
    }

    if (!code || !state) {
      setError('Missing OAuth parameters');
      return;
    }

    // Handle OAuth callback
    handleOAuthCallbackMutation.mutate({
      provider,
      code,
      state,
    });
  }, [searchParams, provider, handleOAuthCallbackMutation]);

  if (error) {
    return (
      <div className="container flex h-screen w-screen flex-col items-center justify-center">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
          <div className="flex flex-col space-y-2 text-center">
            <h1 className="text-2xl font-semibold tracking-tight">
              Authentication Failed
            </h1>
          </div>

          <Alert variant="destructive">
            <Icons.alertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <div className="text-center">
            <button
              onClick={() => router.push('/login')}
              className="text-sm text-muted-foreground hover:text-primary underline"
            >
              Back to login
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <Icons.spinner className="h-6 w-6 text-blue-600 animate-spin" />
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Completing sign in...
          </h1>
          <p className="text-sm text-muted-foreground">
            Please wait while we complete your {provider} authentication.
          </p>
        </div>
      </div>
    </div>
  );
}

export default OAuthCallback;
