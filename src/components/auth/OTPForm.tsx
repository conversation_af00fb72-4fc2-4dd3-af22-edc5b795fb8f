'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Icons } from '@/components/ui/icons';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { api } from '@/trpc/react';

const OTPFormSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  code: z
    .string()
    .length(6, 'OTP must be 6 digits')
    .regex(/^\d+$/, 'OTP must contain only numbers'),
});

type OTPFormData = z.infer<typeof OTPFormSchema>;

interface OTPFormProps {
  purpose?: 'login' | 'verification' | 'password-reset';
  onSuccess?: (data: any) => void;
  onLoginSuccess?: (user: any) => void;
  className?: string;
}

export function OTPForm({ purpose = 'login', onSuccess, onLoginSuccess, className }: OTPFormProps) {
  const [step, setStep] = useState<'email' | 'code'>('email');
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<OTPFormData>({
    resolver: zodResolver(OTPFormSchema),
  });

  const email = watch('email');

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const sendOTPMutation = api.auth.sendOTP.useMutation({
    onSuccess: () => {
      setStep('code');
      setError(null);
      setCountdown(60); // 60 seconds cooldown
      setValue('code', ''); // Clear any existing code
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const verifyOTPMutation = api.auth.verifyOTP.useMutation({
    onSuccess: (data) => {
      setError(null);

      if (purpose === 'login' && data.user) {
        onLoginSuccess?.(data.user);
      } else {
        onSuccess?.(data);
      }
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const onSubmitEmail = async (data: Pick<OTPFormData, 'email'>) => {
    try {
      await sendOTPMutation.mutateAsync({
        email: data.email,
        purpose,
      });
    } catch (_error) {
      // Error is handled by onError callback
    }
  };

  const onSubmitCode = async (data: OTPFormData) => {
    try {
      await verifyOTPMutation.mutateAsync({
        email: data.email,
        code: data.code,
        purpose,
      });
    } catch (_error) {
      // Error is handled by onError callback
    }
  };

  const handleResend = () => {
    if (countdown === 0 && email) {
      sendOTPMutation.mutate({
        email,
        purpose,
      });
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setError(null);
    setCountdown(0);
  };

  if (step === 'email') {
    return (
      <form onSubmit={handleSubmit(onSubmitEmail)} className={`space-y-4 ${className}`}>
        <div className="space-y-2">
          <Label htmlFor="email">Email address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            {...register('email')}
            disabled={isSubmitting}
          />
          {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
        </div>

        {error && (
          <Alert variant="destructive">
            <Icons.alertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Button type="submit" className="w-full" disabled={sendOTPMutation.isPending}>
          {sendOTPMutation.isPending && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Send verification code
        </Button>

        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            We'll send you a 6-digit verification code to{' '}
            {purpose === 'login'
              ? 'sign in'
              : purpose === 'verification'
                ? 'verify your account'
                : 'reset your password'}
            .
          </p>
        </div>
      </form>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmitCode)} className={`space-y-4 ${className}`}>
      <div className="text-center space-y-2">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <Icons.mail className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-medium">Enter verification code</h3>
        <p className="text-sm text-muted-foreground">
          We've sent a 6-digit code to <strong>{email}</strong>
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="code">Verification code</Label>
        <Input
          id="code"
          type="text"
          placeholder="000000"
          maxLength={6}
          className="text-center text-lg tracking-widest"
          {...register('code')}
          disabled={isSubmitting}
          autoComplete="one-time-code"
        />
        {errors.code && <p className="text-sm text-destructive">{errors.code.message}</p>}
      </div>

      {error && (
        <Alert variant="destructive">
          <Icons.alertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Button type="submit" className="w-full" disabled={verifyOTPMutation.isPending}>
        {verifyOTPMutation.isPending && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
        Verify code
      </Button>

      <div className="flex flex-col space-y-2">
        <Button
          type="button"
          variant="outline"
          onClick={handleResend}
          disabled={countdown > 0 || sendOTPMutation.isPending}
        >
          {sendOTPMutation.isPending && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          {countdown > 0 ? `Resend in ${countdown}s` : 'Resend code'}
        </Button>

        <Button type="button" variant="ghost" onClick={handleBackToEmail}>
          Use a different email
        </Button>
      </div>

      <div className="text-center">
        <p className="text-xs text-muted-foreground">The code will expire in 10 minutes.</p>
      </div>
    </form>
  );
}

export default OTPForm;
