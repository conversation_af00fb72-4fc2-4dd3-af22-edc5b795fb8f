import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

import { env } from '@/env';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/profile',
  '/organizations',
  '/jobs/create',
  '/jobs/manage',
  '/applications',
  '/admin',
  '/recruiter',
];

// Admin-only routes
const ADMIN_ROUTES = ['/admin'];

// Recruiter+ routes
const RECRUITER_ROUTES = ['/jobs/create', '/jobs/manage', '/recruiter'];

// Public routes that authenticated users shouldn't access
const AUTH_ROUTES = ['/login', '/register', '/forgot-password'];

interface DecodedToken {
  userId: string;
  email: string;
  role: string;
  type: string;
  iat: number;
  exp: number;
}

function verifyToken(token: string): DecodedToken | null {
  try {
    const decoded = jwt.verify(token, JWT_ACCESS_SECRET) as DecodedToken;
    return decoded.type === 'access' ? decoded : null;
  } catch (error) {
    return null;
  }
}

function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some((route) => pathname.startsWith(route));
}

function isAdminRoute(pathname: string): boolean {
  return ADMIN_ROUTES.some((route) => pathname.startsWith(route));
}

function isRecruiterRoute(pathname: string): boolean {
  return RECRUITER_ROUTES.some((route) => pathname.startsWith(route));
}

function isAuthRoute(pathname: string): boolean {
  return AUTH_ROUTES.some((route) => pathname.startsWith(route));
}

function hasRequiredRole(userRole: string, pathname: string): boolean {
  if (isAdminRoute(pathname)) {
    return userRole === 'admin';
  }

  if (isRecruiterRoute(pathname)) {
    return ['admin', 'recruiter', 'hr'].includes(userRole);
  }

  return true; // For general protected routes, any authenticated user is allowed
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for API routes, static files, and Next.js internals
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Get token from cookies
  const accessToken = request.cookies.get('accessToken')?.value;
  const refreshToken = request.cookies.get('refreshToken')?.value;

  // Verify access token
  const decodedToken = accessToken ? verifyToken(accessToken) : null;
  const isAuthenticated = !!decodedToken;

  // Handle authentication routes
  if (isAuthRoute(pathname)) {
    if (isAuthenticated) {
      // Redirect authenticated users away from auth pages
      const redirectUrl = new URL('/dashboard', request.url);
      return NextResponse.redirect(redirectUrl);
    }
    return NextResponse.next();
  }

  // Handle protected routes
  if (isProtectedRoute(pathname)) {
    if (!isAuthenticated) {
      // If no access token but has refresh token, redirect to refresh
      if (refreshToken) {
        const refreshUrl = new URL('/api/auth/refresh', request.url);
        refreshUrl.searchParams.set('redirect', pathname);
        return NextResponse.redirect(refreshUrl);
      }

      // No valid tokens, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Check role-based access
    if (!hasRequiredRole(decodedToken.role, pathname)) {
      const unauthorizedUrl = new URL('/unauthorized', request.url);
      return NextResponse.redirect(unauthorizedUrl);
    }
  }

  // Add user info to headers for server components
  if (isAuthenticated && decodedToken) {
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-user-id', decodedToken.userId);
    requestHeaders.set('x-user-email', decodedToken.email);
    requestHeaders.set('x-user-role', decodedToken.role);

    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
