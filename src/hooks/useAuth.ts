'use client';

import { useContext } from 'react';
import { useRouter } from 'next/navigation';
import AuthContext, { User } from '@/contexts/AuthContext';

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for login with redirect
export function useLogin() {
  const { login } = useAuth();
  const router = useRouter();

  const loginWithRedirect = async (
    email: string,
    password: string,
    rememberMe = false,
    redirectTo = '/dashboard'
  ) => {
    const result = await login(email, password, rememberMe);
    
    if (result.success) {
      router.push(redirectTo);
    }
    
    return result;
  };

  return { login, loginWithRedirect };
}

// Hook for logout with redirect
export function useLogout() {
  const { logout } = useAuth();
  const router = useRouter();

  const logoutWithRedirect = async (redirectTo = '/') => {
    await logout();
    router.push(redirectTo);
  };

  return { logout, logoutWithRedirect };
}

// Hook for registration with redirect
export function useRegister() {
  const { register } = useAuth();
  const router = useRouter();

  const registerWithRedirect = async (
    userData: {
      email: string;
      password: string;
      firstName: string;
      lastName: string;
      phone?: string;
      acceptedTerms: boolean;
    },
    redirectTo = '/dashboard'
  ) => {
    const result = await register(userData);
    
    if (result.success) {
      router.push(redirectTo);
    }
    
    return result;
  };

  return { register, registerWithRedirect };
}

// Hook for checking permissions
export function usePermissions() {
  const { user, isAuthenticated } = useAuth();

  const hasRole = (requiredRole: string | string[]) => {
    if (!isAuthenticated || !user) return false;
    
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    return roles.includes(user.role);
  };

  const isAdmin = () => hasRole('admin');
  const isRecruiter = () => hasRole(['admin', 'recruiter', 'hr']);
  const isCandidate = () => hasRole('candidate');
  const isHR = () => hasRole(['admin', 'hr']);

  // Role hierarchy check
  const hasMinimumRole = (minimumRole: string) => {
    if (!isAuthenticated || !user) return false;

    const roleHierarchy: Record<string, number> = {
      candidate: 1,
      interviewer: 2,
      hr: 3,
      recruiter: 4,
      admin: 5,
    };

    const userLevel = roleHierarchy[user.role] || 0;
    const requiredLevel = roleHierarchy[minimumRole] || 0;

    return userLevel >= requiredLevel;
  };

  return {
    hasRole,
    isAdmin,
    isRecruiter,
    isCandidate,
    isHR,
    hasMinimumRole,
    user,
    isAuthenticated,
  };
}

// Hook for protected actions
export function useProtectedAction() {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  const executeIfAuthenticated = <T extends any[], R>(
    action: (...args: T) => R,
    redirectTo = '/login'
  ) => {
    return (...args: T): R | void => {
      if (!isAuthenticated) {
        router.push(redirectTo);
        return;
      }
      return action(...args);
    };
  };

  const executeIfRole = <T extends any[], R>(
    action: (...args: T) => R,
    requiredRole: string | string[],
    fallback?: () => void
  ) => {
    return (...args: T): R | void => {
      if (!isAuthenticated || !user) {
        router.push('/login');
        return;
      }

      const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
      if (!roles.includes(user.role)) {
        if (fallback) {
          fallback();
        } else {
          router.push('/unauthorized');
        }
        return;
      }

      return action(...args);
    };
  };

  return {
    executeIfAuthenticated,
    executeIfRole,
  };
}

// Hook for auth state management
export function useAuthState() {
  const { user, isLoading, isAuthenticated, refreshAuth } = useAuth();

  const getUserDisplayName = () => {
    if (!user) return '';
    return `${user.firstName} ${user.lastName}`.trim();
  };

  const getUserInitials = () => {
    if (!user) return '';
    const firstInitial = user.firstName?.charAt(0) || '';
    const lastInitial = user.lastName?.charAt(0) || '';
    return `${firstInitial}${lastInitial}`.toUpperCase();
  };

  const isEmailVerified = () => {
    return user?.isEmailVerified || false;
  };

  const getUserRole = () => {
    return user?.role || '';
  };

  const getUserStatus = () => {
    return user?.status || '';
  };

  return {
    user,
    isLoading,
    isAuthenticated,
    refreshAuth,
    getUserDisplayName,
    getUserInitials,
    isEmailVerified,
    getUserRole,
    getUserStatus,
  };
}

// Hook for conditional rendering based on auth
export function useAuthRender() {
  const { isAuthenticated, isLoading, user } = useAuth();

  const renderIfAuthenticated = (component: React.ReactNode) => {
    return isAuthenticated ? component : null;
  };

  const renderIfNotAuthenticated = (component: React.ReactNode) => {
    return !isAuthenticated && !isLoading ? component : null;
  };

  const renderIfRole = (component: React.ReactNode, requiredRole: string | string[]) => {
    if (!isAuthenticated || !user) return null;
    
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    return roles.includes(user.role) ? component : null;
  };

  const renderIfLoading = (component: React.ReactNode) => {
    return isLoading ? component : null;
  };

  return {
    renderIfAuthenticated,
    renderIfNotAuthenticated,
    renderIfRole,
    renderIfLoading,
  };
}

export default useAuth;
