import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import {
  CreateOrganizationSchema,
  OrganizationStatusSchema,
  UpdateOrganizationSchema,
} from '@/@types/organization.types';
import { Organization } from '@/db/models';

import { adminProcedure, createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import {
  buildMongoFilter,
  buildMongoSort,
  calculateSkip,
  createPaginatedResponse,
  handleApiError,
  IdSchema,
  PaginationInput,
  PaginationSchema,
  SearchSchema,
  softDelete,
} from '../utils/common';

export const organizationRouter = createTRPCRouter({
  /**
   * Create a new organization
   */
  create: protectedProcedure.input(CreateOrganizationSchema).mutation(async ({ input, ctx }) => {
    try {
      // Add the current user as the owner
      const organizationData = {
        ...input,
        ownerId: ctx.user.id,
      };

      const organization = await Organization.createOrganization(organizationData);
      return organization;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get organization by ID
   */
  getById: publicProcedure.input(IdSchema).query(async ({ input }) => {
    try {
      const organization = await Organization.findOne({
        $or: [{ organizationId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found',
        });
      }

      return organization;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get organization by slug
   */
  getBySlug: publicProcedure.input(z.object({ slug: z.string() })).query(async ({ input }) => {
    try {
      const organization = await Organization.findOne({
        slug: input.slug,
        isDeleted: { $ne: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found',
        });
      }

      return organization;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * List organizations with pagination and search
   */
  list: publicProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            status: OrganizationStatusSchema.optional(),
            type: z.string().optional(),
            industry: z.string().optional(),
            isVerified: z.boolean().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input }) => {
      try {
        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build filter object
        const mongoFilter = buildMongoFilter({
          ...filters,
          search: search.query,
        });

        // Build sort object
        const mongoSort = buildMongoSort(sortBy, sortOrder);

        // Execute query with pagination
        const skip = calculateSkip(page, limit);

        const [organizations, total] = await Promise.all([
          Organization.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          Organization.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(organizations, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update organization
   */
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateOrganizationSchema,
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const organization = await Organization.findOne({
          $or: [{ organizationId: input.id }, { _id: input.id }],
          isDeleted: { $ne: true },
        });

        if (!organization) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found',
          });
        }

        // Check if user has permission to update
        if (organization.ownerId !== ctx.user.id && ctx.user.role !== 'admin') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to update this organization',
          });
        }

        const updatedOrganization = await Organization.findByIdAndUpdate(
          organization._id,
          { ...input.data, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        return updatedOrganization;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Soft delete organization
   */
  delete: protectedProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const organization = await Organization.findOne({
        $or: [{ organizationId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found',
        });
      }

      // Check if user has permission to delete
      if (organization.ownerId !== ctx.user.id && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to delete this organization',
        });
      }

      await softDelete(Organization, organization._id.toString(), ctx.user.id);

      return { success: true, message: 'Organization deleted successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Update organization status
   */
  updateStatus: adminProcedure
    .input(
      z.object({
        id: z.string(),
        status: OrganizationStatusSchema,
      })
    )
    .mutation(async ({ input }) => {
      try {
        const organization = await Organization.findByIdAndUpdate(
          input.id,
          { status: input.status, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        if (!organization) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Organization not found',
          });
        }

        return organization;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify organization
   */
  verify: adminProcedure.input(IdSchema).mutation(async ({ input }) => {
    try {
      const organization = await Organization.findByIdAndUpdate(
        input.id,
        {
          isVerified: true,
          verifiedAt: new Date(),
          updatedAt: new Date(),
        },
        { new: true, runValidators: true }
      );

      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found',
        });
      }

      return organization;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get organization statistics
   */
  getStats: protectedProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    try {
      const organization = await Organization.findOne({
        $or: [{ organizationId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Organization not found',
        });
      }

      // Check if user has permission to view stats
      if (organization.ownerId !== ctx.user.id && ctx.user.role !== 'admin') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to view organization statistics',
        });
      }

      return {
        memberCount: organization.memberCount,
        jobCount: organization.jobCount,
        isVerified: organization.isVerified,
        status: organization.status,
        createdAt: organization.createdAt,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get organizations owned by current user
   */
  getMyOrganizations: protectedProcedure
    .input(PaginationSchema.optional())
    .query(async ({ input = {}, ctx }) => {
      try {
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = input;

        const mongoFilter = {
          ownerId: ctx.user.id,
          isDeleted: { $ne: true },
        };

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [organizations, total] = await Promise.all([
          Organization.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          Organization.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(organizations, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),
});
