import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import { CreateUserSchema, LoginSchema } from '@/@types/user.types';
import { User } from '@/db/models';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import { handleApiError } from '../utils/common';

export const authRouter = createTRPCRouter({
  /**
   * User login
   */
  login: publicProcedure.input(LoginSchema).mutation(async ({ input }) => {
    try {
      const { user, error } = await User.authenticate(input.email, input.password);

      if (error || !user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: error || 'Invalid credentials',
        });
      }

      // In a real implementation, you would generate a JWT token here
      const token = `mock_jwt_token_${user.userId}`;

      return {
        user: {
          id: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
        },
        token,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * User registration
   */
  register: publicProcedure
    .input(
      CreateUserSchema.extend({
        role: z.literal('candidate').default('candidate'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const userData = {
          ...input,
          role: 'candidate' as const,
        };

        const user = await User.createUser(userData);

        // In a real implementation, you would generate a JWT token here
        const token = `mock_jwt_token_${user.userId}`;

        return {
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
          token,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get current user profile
   */
  me: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await User.findOne({
        userId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return {
        id: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        phone: user.phone,
        avatar: user.avatar,
        timezone: user.timezone,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Logout (placeholder - in real implementation would invalidate token)
   */
  logout: protectedProcedure.mutation(async () => {
    // In a real implementation, you would invalidate the JWT token
    return { success: true, message: 'Logged out successfully' };
  }),

  /**
   * Refresh token (placeholder)
   */
  refreshToken: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      // In a real implementation, you would validate the refresh token and generate a new access token
      const newToken = `mock_jwt_token_${ctx.user.id}_refreshed`;

      return {
        token: newToken,
        user: ctx.user,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Request password reset
   */
  requestPasswordReset: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByEmail(input.email);

        if (!user) {
          // Don't reveal if email exists or not for security
          return { success: true, message: 'If the email exists, a reset link has been sent' };
        }

        // In a real implementation, you would:
        // 1. Generate a secure reset token
        // 2. Save it to the user record with expiration
        // 3. Send an email with the reset link

        const resetToken = `reset_${Date.now()}_${user.userId}`;
        await User.findByIdAndUpdate(user._id, {
          passwordResetToken: resetToken,
          passwordResetExpires: new Date(Date.now() + 3600000), // 1 hour
        });

        return { success: true, message: 'If the email exists, a reset link has been sent' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Reset password
   */
  resetPassword: publicProcedure
    .input(
      z.object({
        token: z.string(),
        newPassword: z.string().min(8),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByResetToken(input.token);

        if (!user) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired reset token',
          });
        }

        // Update password and clear reset token
        user.password = input.newPassword;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();

        return { success: true, message: 'Password reset successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify email (placeholder)
   */
  verifyEmail: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        // In a real implementation, you would validate the email verification token
        // For now, we'll just return success
        console.log('Email verification token:', input.token);
        return { success: true, message: 'Email verified successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),
});
