import { TRPCError } from '@trpc/server';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { z } from 'zod';

import { CreateUserSchema, LoginSchema } from '@/@types/user.types';
import { User } from '@/db/models';
import { env } from '@/env';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';
import { handleApiError } from '../utils/common';

// JWT configuration
const JWT_ACCESS_SECRET = env.JWT_ACCESS_SECRET;
const JWT_REFRESH_SECRET = env.JWT_REFRESH_SECRET;
const ACCESS_TOKEN_EXPIRES_IN = '15m'; // 15 minutes
const REFRESH_TOKEN_EXPIRES_IN = '7d'; // 7 days

// Cookie configuration
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  path: '/',
};

const ACCESS_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 15 * 60, // 15 minutes in seconds
};

const REFRESH_COOKIE_OPTIONS = {
  ...COOKIE_OPTIONS,
  maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
};

// Token generation utilities
function generateAccessToken(userId: string, email: string, role: string) {
  return jwt.sign({ userId, email, role, type: 'access' }, JWT_ACCESS_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRES_IN,
  });
}

function generateRefreshToken(userId: string) {
  return jwt.sign({ userId, type: 'refresh' }, JWT_REFRESH_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRES_IN,
  });
}

function verifyRefreshToken(token: string) {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET) as {
      userId: string;
      type: string;
    };
  } catch (_error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Invalid refresh token',
    });
  }
}

// Set authentication cookies (for server actions)
async function setAuthCookies(accessToken: string, refreshToken: string) {
  const cookieStore = await cookies();
  cookieStore.set('accessToken', accessToken, ACCESS_COOKIE_OPTIONS);
  cookieStore.set('refreshToken', refreshToken, REFRESH_COOKIE_OPTIONS);
}

// Clear authentication cookies (for server actions)
async function clearAuthCookies() {
  const cookieStore = await cookies();
  cookieStore.delete('accessToken');
  cookieStore.delete('refreshToken');
}

export const authRouter = createTRPCRouter({
  /**
   * User login
   */
  login: publicProcedure.input(LoginSchema).mutation(async ({ input }) => {
    try {
      const { user, error } = await User.authenticate(input.email, input.password);

      if (error || !user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: error || 'Invalid credentials',
        });
      }

      // Generate tokens
      const accessToken = generateAccessToken(user.userId, user.email, user.role);
      const refreshToken = generateRefreshToken(user.userId);

      // Set cookies
      setAuthCookies(accessToken, refreshToken);

      // Update last login
      await User.findByIdAndUpdate(user._id, { lastLoginAt: new Date() });

      return {
        user: {
          id: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
        },
        success: true,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * User registration
   */
  register: publicProcedure
    .input(
      CreateUserSchema.extend({
        role: z.literal('candidate').default('candidate'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const userData = {
          ...input,
          role: 'candidate' as const,
        };

        const user = await User.createUser(userData);

        // Generate tokens
        const accessToken = generateAccessToken(user.userId, user.email, user.role);
        const refreshToken = generateRefreshToken(user.userId);

        // Set cookies
        setAuthCookies(accessToken, refreshToken);

        return {
          user: {
            id: user.userId,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            isEmailVerified: user.isEmailVerified,
          },
          success: true,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get current user profile
   */
  me: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await User.findOne({
        userId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return {
        id: user.userId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        phone: user.phone,
        avatar: user.avatar,
        timezone: user.timezone,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Logout
   */
  logout: protectedProcedure.mutation(async () => {
    try {
      // Clear authentication cookies
      clearAuthCookies();

      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Refresh token
   */
  refreshToken: publicProcedure.mutation(async ({ ctx }) => {
    try {
      const refreshToken = ctx.cookies.refreshToken;

      if (!refreshToken) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'No refresh token provided',
        });
      }

      // Verify refresh token
      const decoded = verifyRefreshToken(refreshToken);

      // Get user from database
      const user = await User.findOne({
        userId: decoded.userId,
        isDeleted: { $ne: true },
      });

      if (!user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'User not found',
        });
      }

      // Generate new tokens
      const newAccessToken = generateAccessToken(user.userId, user.email, user.role);
      const newRefreshToken = generateRefreshToken(user.userId);

      // Set new cookies
      await setAuthCookies(newAccessToken, newRefreshToken);

      return {
        user: {
          id: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          status: user.status,
          isEmailVerified: user.isEmailVerified,
        },
        success: true,
      };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Request password reset
   */
  requestPasswordReset: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByEmail(input.email);

        if (!user) {
          // Don't reveal if email exists or not for security
          return { success: true, message: 'If the email exists, a reset link has been sent' };
        }

        // In a real implementation, you would:
        // 1. Generate a secure reset token
        // 2. Save it to the user record with expiration
        // 3. Send an email with the reset link

        const resetToken = `reset_${Date.now()}_${user.userId}`;
        await User.findByIdAndUpdate(user._id, {
          passwordResetToken: resetToken,
          passwordResetExpires: new Date(Date.now() + 3600000), // 1 hour
        });

        return { success: true, message: 'If the email exists, a reset link has been sent' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Reset password
   */
  resetPassword: publicProcedure
    .input(
      z.object({
        token: z.string(),
        newPassword: z.string().min(8),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const user = await User.findByResetToken(input.token);

        if (!user) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired reset token',
          });
        }

        // Update password and clear reset token
        user.password = input.newPassword;
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();

        return { success: true, message: 'Password reset successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Send magic link
   */
  sendMagicLink: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
        redirectTo: z.string().optional(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const response = await fetch(`${env.APP_URL}/api/auth/magic-link/send`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: data.message || 'Failed to send magic link',
          });
        }

        return data;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Send OTP
   */
  sendOTP: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const response = await fetch(`${env.APP_URL}/api/auth/otp/send`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: data.message || 'Failed to send OTP',
          });
        }

        return data;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify OTP
   */
  verifyOTP: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
        code: z.string().length(6),
        purpose: z.enum(['login', 'verification', 'password-reset']).default('login'),
      })
    )
    .mutation(async ({ input }) => {
      try {
        const response = await fetch(`${env.APP_URL}/api/auth/otp/verify`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: data.message || 'OTP verification failed',
          });
        }

        return data;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Verify email (placeholder)
   */
  verifyEmail: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        // In a real implementation, you would validate the email verification token
        // For now, we'll just return success
        console.log('Email verification token:', input.token);
        return { success: true, message: 'Email verified successfully' };
      } catch (error) {
        handleApiError(error);
      }
    }),
});
