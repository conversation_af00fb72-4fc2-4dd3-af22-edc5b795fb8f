import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import {
  ApplicationStatusSchema,
  CreateJobApplicationInput,
  CreateJobApplicationSchema,
  UpdateJobApplicationSchema,
} from '@/@types/job-application.types';
import { Job, JobApplication, OrganizationMember } from '@/db/models';

import { createTRPCRouter, protectedProcedure, recruiterProcedure } from '../trpc';
import {
  buildMongoFilter,
  buildMongoSort,
  calculateSkip,
  createPaginatedResponse,
  handleApiError,
  IdSchema,
  PaginationInput,
  PaginationSchema,
  SearchSchema,
  softDelete,
} from '../utils/common';

export const jobApplicationRouter = createTRPCRouter({
  /**
   * Submit a job application
   */
  create: protectedProcedure.input(CreateJobApplicationSchema).mutation(async ({ input, ctx }) => {
    try {
      // Verify the job exists and is published
      const job = await Job.findOne({
        $or: [{ jobId: input.jobId }, { _id: input.jobId }],
        status: 'published',
        isDeleted: { $ne: true },
      });

      if (!job) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Job not found or not available for applications',
        });
      }

      // Check if user has already applied for this job
      const existingApplication = await JobApplication.findOne({
        jobId: job.jobId,
        applicantId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (existingApplication) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'You have already applied for this job',
        });
      }

      const applicationData: CreateJobApplicationInput = {
        ...input,
        jobId: job.jobId,
        applicantId: ctx.user.id,
      };

      const application = await JobApplication.createApplication(applicationData);

      // Update job application count
      await Job.findByIdAndUpdate(job._id, { $inc: { applicationCount: 1 } });

      return application;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Get application by ID
   */
  getById: protectedProcedure.input(IdSchema).query(async ({ input, ctx }) => {
    try {
      const application = await JobApplication.findOne({
        $or: [{ applicationId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!application) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Application not found',
        });
      }

      // Check if user has permission to view this application
      const canView =
        application.applicantId === ctx.user.id || // Applicant can view their own application
        ctx.user.role === 'admin';

      if (!canView) {
        // Check if user is a recruiter/member of the organization
        const job = await Job.findOne({ jobId: application.jobId });
        if (job) {
          const member = await OrganizationMember.findOne({
            organizationId: job.organizationId,
            userId: ctx.user.id,
            status: 'active',
            isDeleted: { $ne: true },
          });

          if (!member || !member.permissions.canViewApplications) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'You do not have permission to view this application',
            });
          }
        } else {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to view this application',
          });
        }
      }

      return application;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * List applications with pagination and search
   */
  list: recruiterProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            jobId: z.string().optional(),
            organizationId: z.string().optional(),
            status: ApplicationStatusSchema.optional(),
            source: z.string().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        // Build base filter
        const mongoFilter: any = buildMongoFilter({
          ...filters,
          search: search.query,
        });

        // If not admin, filter by organization membership
        if (ctx.user.role !== 'admin') {
          if (filters.organizationId) {
            // Check if user has permission to view applications in this organization
            const member = await OrganizationMember.findOne({
              organizationId: filters.organizationId,
              userId: ctx.user.id,
              status: 'active',
              isDeleted: { $ne: true },
            });

            if (!member || !member.permissions.canViewApplications) {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'You do not have permission to view applications in this organization',
              });
            }
          } else {
            // Get all organizations where user can view applications
            const memberships = await OrganizationMember.find({
              userId: ctx.user.id,
              status: 'active',
              'permissions.canViewApplications': true,
              isDeleted: { $ne: true },
            }).select('organizationId');

            const organizationIds = memberships.map((m) => m.organizationId);

            if (organizationIds.length === 0) {
              return createPaginatedResponse([], 0, page, limit);
            }

            // Get jobs from these organizations
            const jobs = await Job.find({
              organizationId: { $in: organizationIds },
              isDeleted: { $ne: true },
            }).select('jobId');

            const jobIds = jobs.map((j) => j.jobId);
            mongoFilter.jobId = { $in: jobIds };
          }
        }

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [applications, total] = await Promise.all([
          JobApplication.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          JobApplication.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(applications, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * List applications for a specific job
   */
  listByJob: recruiterProcedure
    .input(
      z.object({
        jobId: z.string(),
        pagination: PaginationSchema.optional(),
        search: SearchSchema.optional(),
        filters: z
          .object({
            status: ApplicationStatusSchema.optional(),
            source: z.string().optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Verify job exists and user has permission
        const job = await Job.findOne({
          $or: [{ jobId: input.jobId }, { _id: input.jobId }],
          isDeleted: { $ne: true },
        });

        if (!job) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Job not found',
          });
        }

        // Check permissions
        if (ctx.user.role !== 'admin') {
          const member = await OrganizationMember.findOne({
            organizationId: job.organizationId,
            userId: ctx.user.id,
            status: 'active',
            isDeleted: { $ne: true },
          });

          if (!member || !member.permissions.canViewApplications) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'You do not have permission to view applications for this job',
            });
          }
        }

        const { pagination = {}, search = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        const mongoFilter = buildMongoFilter({
          ...filters,
          jobId: job.jobId,
          search: search.query,
        });

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [applications, total] = await Promise.all([
          JobApplication.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          JobApplication.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(applications, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get user's own applications
   */
  getMyApplications: protectedProcedure
    .input(
      z.object({
        pagination: PaginationSchema.optional(),
        filters: z
          .object({
            status: ApplicationStatusSchema.optional(),
          })
          .optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const { pagination = {}, filters = {} } = input;
        const { page = 1, limit = 10, sortBy, sortOrder = 'desc' } = pagination as PaginationInput;

        const mongoFilter = buildMongoFilter({
          ...filters,
          applicantId: ctx.user.id,
        });

        const mongoSort = buildMongoSort(sortBy, sortOrder);
        const skip = calculateSkip(page, limit);

        const [applications, total] = await Promise.all([
          JobApplication.find(mongoFilter).sort(mongoSort).skip(skip).limit(limit).lean(),
          JobApplication.countDocuments(mongoFilter),
        ]);

        return createPaginatedResponse(applications, total, page, limit);
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update application status
   */
  updateStatus: recruiterProcedure
    .input(
      z.object({
        id: z.string(),
        status: ApplicationStatusSchema,
        reviewNotes: z.string().optional(),
        rejectionReason: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const application = await JobApplication.findOne({
          $or: [{ applicationId: input.id }, { _id: input.id }],
          isDeleted: { $ne: true },
        });

        if (!application) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Application not found',
          });
        }

        // Check permissions
        if (ctx.user.role !== 'admin') {
          const job = await Job.findOne({ jobId: application.jobId });
          if (!job) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Associated job not found',
            });
          }

          const member = await OrganizationMember.findOne({
            organizationId: job.organizationId,
            userId: ctx.user.id,
            status: 'active',
            isDeleted: { $ne: true },
          });

          if (!member || !member.permissions.canManageApplications) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'You do not have permission to update this application',
            });
          }
        }

        const updateData: any = {
          status: input.status,
          updatedAt: new Date(),
        };

        if (input.reviewNotes) updateData.reviewNotes = input.reviewNotes;
        if (input.rejectionReason) updateData.rejectionReason = input.rejectionReason;

        const updatedApplication = await JobApplication.findByIdAndUpdate(
          application._id,
          updateData,
          { new: true, runValidators: true }
        );

        return updatedApplication;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Update application (by applicant)
   */
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        data: UpdateJobApplicationSchema.omit({ status: true }), // Applicants can't change status
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const application = await JobApplication.findOne({
          $or: [{ applicationId: input.id }, { _id: input.id }],
          applicantId: ctx.user.id,
          isDeleted: { $ne: true },
        });

        if (!application) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Application not found or you do not have permission to update it',
          });
        }

        // Only allow updates if application is in submitted or under_review status
        if (!['submitted', 'under_review'].includes(application.status)) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Cannot update application in current status',
          });
        }

        const updatedApplication = await JobApplication.findByIdAndUpdate(
          application._id,
          { ...input.data, updatedAt: new Date() },
          { new: true, runValidators: true }
        );

        return updatedApplication;
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Withdraw application
   */
  withdraw: protectedProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const application = await JobApplication.findOne({
        $or: [{ applicationId: input.id }, { _id: input.id }],
        applicantId: ctx.user.id,
        isDeleted: { $ne: true },
      });

      if (!application) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Application not found or you do not have permission to withdraw it',
        });
      }

      // Only allow withdrawal if application is not already processed
      if (
        ['offer_accepted', 'offer_declined', 'rejected', 'withdrawn'].includes(application.status)
      ) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot withdraw application in current status',
        });
      }

      const updatedApplication = await JobApplication.findByIdAndUpdate(
        application._id,
        {
          status: 'withdrawn',
          updatedAt: new Date(),
        },
        { new: true, runValidators: true }
      );

      return updatedApplication;
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Soft delete application (Admin/Recruiter only)
   */
  delete: recruiterProcedure.input(IdSchema).mutation(async ({ input, ctx }) => {
    try {
      const application = await JobApplication.findOne({
        $or: [{ applicationId: input.id }, { _id: input.id }],
        isDeleted: { $ne: true },
      });

      if (!application) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Application not found',
        });
      }

      // Check permissions
      if (ctx.user.role !== 'admin') {
        const job = await Job.findOne({ jobId: application.jobId });
        if (!job) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Associated job not found',
          });
        }

        const member = await OrganizationMember.findOne({
          organizationId: job.organizationId,
          userId: ctx.user.id,
          status: 'active',
          isDeleted: { $ne: true },
        });

        if (!member || !member.permissions.canManageApplications) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'You do not have permission to delete this application',
          });
        }
      }

      await softDelete(JobApplication, application._id.toString(), ctx.user.id);

      // Update job application count
      const job = await Job.findOne({ jobId: application.jobId });
      if (job) {
        await Job.findByIdAndUpdate(job._id, { $inc: { applicationCount: -1 } });
      }

      return { success: true, message: 'Application deleted successfully' };
    } catch (error) {
      handleApiError(error);
    }
  }),

  /**
   * Bulk update application status
   */
  bulkUpdateStatus: recruiterProcedure
    .input(
      z.object({
        applicationIds: z.array(z.string()).min(1),
        status: ApplicationStatusSchema,
        reviewNotes: z.string().optional(),
        rejectionReason: z.string().optional(),
        organizationId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Check permissions for the organization
        if (ctx.user.role !== 'admin') {
          const member = await OrganizationMember.findOne({
            organizationId: input.organizationId,
            userId: ctx.user.id,
            status: 'active',
            isDeleted: { $ne: true },
          });

          if (!member || !member.permissions.canManageApplications) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'You do not have permission to update applications in this organization',
            });
          }
        }

        // Get jobs from the organization to filter applications
        const jobs = await Job.find({
          organizationId: input.organizationId,
          isDeleted: { $ne: true },
        }).select('jobId');

        const jobIds = jobs.map((j) => j.jobId);

        const updateData: any = {
          status: input.status,
          updatedAt: new Date(),
        };

        if (input.reviewNotes) updateData.reviewNotes = input.reviewNotes;
        if (input.rejectionReason) updateData.rejectionReason = input.rejectionReason;

        const result = await JobApplication.updateMany(
          {
            _id: { $in: input.applicationIds },
            jobId: { $in: jobIds },
            isDeleted: { $ne: true },
          },
          updateData
        );

        return {
          success: true,
          message: `Updated ${result.modifiedCount} applications`,
          modifiedCount: result.modifiedCount,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),

  /**
   * Get application statistics
   */
  getStats: recruiterProcedure
    .input(
      z.object({
        organizationId: z.string().optional(),
        jobId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const filter: any = { isDeleted: { $ne: true } };

        if (input.organizationId) {
          // Check permissions for specific organization
          if (ctx.user.role !== 'admin') {
            const member = await OrganizationMember.findOne({
              organizationId: input.organizationId,
              userId: ctx.user.id,
              status: 'active',
              isDeleted: { $ne: true },
            });

            if (!member || !member.permissions.canViewApplications) {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'You do not have permission to view statistics for this organization',
              });
            }
          }

          // Get jobs from the organization
          const jobs = await Job.find({
            organizationId: input.organizationId,
            isDeleted: { $ne: true },
          }).select('jobId');

          const jobIds = jobs.map((j) => j.jobId);
          filter.jobId = { $in: jobIds };
        } else if (input.jobId) {
          // Check permissions for specific job
          const job = await Job.findOne({
            $or: [{ jobId: input.jobId }, { _id: input.jobId }],
            isDeleted: { $ne: true },
          });

          if (!job) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Job not found',
            });
          }

          if (ctx.user.role !== 'admin') {
            const member = await OrganizationMember.findOne({
              organizationId: job.organizationId,
              userId: ctx.user.id,
              status: 'active',
              isDeleted: { $ne: true },
            });

            if (!member || !member.permissions.canViewApplications) {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'You do not have permission to view statistics for this job',
              });
            }
          }

          filter.jobId = job.jobId;
        } else if (ctx.user.role !== 'admin') {
          // For non-admins without specific org/job, get all accessible applications
          const memberships = await OrganizationMember.find({
            userId: ctx.user.id,
            status: 'active',
            'permissions.canViewApplications': true,
            isDeleted: { $ne: true },
          }).select('organizationId');

          const organizationIds = memberships.map((m) => m.organizationId);

          if (organizationIds.length === 0) {
            return {
              totalApplications: 0,
              statusDistribution: {},
              sourceDistribution: {},
            };
          }

          const jobs = await Job.find({
            organizationId: { $in: organizationIds },
            isDeleted: { $ne: true },
          }).select('jobId');

          const jobIds = jobs.map((j) => j.jobId);
          filter.jobId = { $in: jobIds };
        }

        const [totalApplications, statusDistribution, sourceDistribution] = await Promise.all([
          JobApplication.countDocuments(filter),
          JobApplication.aggregate([
            { $match: filter },
            { $group: { _id: '$status', count: { $sum: 1 } } },
          ]),
          JobApplication.aggregate([
            { $match: filter },
            { $group: { _id: '$source', count: { $sum: 1 } } },
          ]),
        ]);

        const statusStats = statusDistribution.reduce(
          (acc, item) => {
            acc[item._id] = item.count;
            return acc;
          },
          {} as Record<string, number>
        );

        const sourceStats = sourceDistribution.reduce(
          (acc, item) => {
            acc[item._id] = item.count;
            return acc;
          },
          {} as Record<string, number>
        );

        return {
          totalApplications,
          statusDistribution: statusStats,
          sourceDistribution: sourceStats,
        };
      } catch (error) {
        handleApiError(error);
      }
    }),
});
